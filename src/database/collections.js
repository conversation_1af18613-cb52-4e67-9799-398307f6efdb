const dayjs = require("dayjs");
const advancedFormat = require("dayjs/plugin/advancedFormat");
const utc = require('dayjs/plugin/utc')
const timezone = require('dayjs/plugin/timezone')

dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(advancedFormat);

class Collection {
    constructor(doc) {
        this.doc = doc;
    }

    // Sport emoji mapping
    getSportEmoji() {
        const sportEmojis = {
            'NBA': '🏀',
            'NBA1Q': '🏀',
            'OBBALL': '🏀',
            'WNBA': '🏀',
            'NCAAB': '🏀',
            'EURO': '🏀',
            'NFL': '🏈',
            'NHL': '🏒',
            'DARTS': '🎯',
            'SOCCER': '⚽️',
            'MLB': '⚾️',
            'HRDERBY': '⚾️',
            'LAX': '🥍',
            'TENNIS': '🎾',
            'CRICKET': '🏏',
            'MMA': '🥊',
            'GOLF': '⛳️',
            'CFB': '🏈'
        };
        return sportEmojis[this.doc.league] || '';
    }

    getTitle() {
        const emoji = this.getSportEmoji();
        return `${emoji} ${this.doc.league} - ${this.doc.player_name}`;
    }

    getDescription() {
        return null;
    }

    getColor() {
        let avg_ev_color;
        const avg_ev = this.doc.avg_ev;
        if (avg_ev >= 0.05) { // greater than or equal to 5%
            avg_ev_color = '#39ff14'; // GREEN
        } else if (avg_ev > 0.03 && avg_ev < 0.05) { // greater than 3% but less than 5%
            avg_ev_color = '#faed27'; // YELLOW
        } else { // less than or equal to 3.0%
            avg_ev_color = '#ff3131'; // RED
        }
        return avg_ev_color;
    }

    getAuthor() { }

    getLine() {
        return this.doc.line;
    }

    getStatType() {
        return this.doc.stat_type;
    }

    getDatawisePick() {
        return this.doc.datawise_pick;
    }

    getOdds(source, pick) {
        const bookmaker = this.doc.sharps.find(sharp => sharp.source === source);
        if (!bookmaker) throw new Error("No sharp found");

        return bookmaker[`${pick.toLowerCase()}_odds_american`];
    }

    getSharpEV() {
        return this.doc.sharp_ev_display;
    }

    getAvgEV() {
        return this.doc.avg_ev_display;
    }

    getAvgEVRaw() {
        return this.doc.avg_ev;
    }

    setModelEv() {
        let modelEv;
        if (this.doc.sharp_ev !== null) {
            modelEv = this.doc.sharp_ev;
        } else {
            modelEv = this.doc.avg_ev;
        }
        return modelEv;
    }

    getTiePercent() {
        return parseFloat(this.doc.model_tie_percent);
    }

    getDatawisePercent() {
        return this.doc.datawise_percent;
    }

    getDatawisePercentNumeric() {
        return parseFloat(this.doc.datawise_percent);
    }

    getMatchup() {
        let rawStartTime;
        if (this.doc.source === "Sleeper") {
            const bookmaker = this.doc.sharps.find(({ source }) => source === "DraftKings" || source === "Caesars");
            rawStartTime = bookmaker?.start_time;
        } else {
            rawStartTime = this.doc.start_time;
        }

        // No DK or Czrs in sharp sources
        if (!rawStartTime) {
            if (this.doc.sharps && this.doc.sharps.length > 0) {
                rawStartTime = this.doc.sharps[0].start_time;
            } else {
                // Handle case where sharps array might be empty or missing start_time
                console.warn(`Missing start_time for doc: ${this.doc.proj_id || 'N/A'}`);
                return this.doc.matchup || 'Matchup time unavailable';
            }
        }


        const startTime = dayjs(new Date(`${rawStartTime}`));
        const prettyDate = startTime.tz("America/Toronto").format("ddd, MMM D, h:mm A EST");

        return `${this.doc.matchup} - ${prettyDate}`;
    }

    getFirstFieldName() {
        return `${this.doc.source} EV`;
    }

    getMulti() { }
    getAppOdds() { }
}

class BetMGM extends Collection {
    getAuthor() {
        return {
            name: 'BetMGM',
            iconURL: 'https://cdn.discordapp.com/attachments/1067536544346415144/1201639770841956402/G51CUqFC_400x400.jpg?ex=65ca8d3e&is=65b8183e&hm=3b9659edc61fb15ad3a4613e94cba7d3c86d4262af6f8ecf200dc076abc9151c&'
        };
    }

    getAppOdds() {
        const datawisePick = this.doc.datawise_pick;

        if (datawisePick === 'Over') {
            return this.doc.over_odds_american;

        } else if (datawisePick === 'Under') {
            return this.doc.under_odds_american;

        } else {
            return null;
        }
    }
}

class BetSaracen extends Collection {
    getAuthor() {
        return {
            name: 'BetSaracen',
            iconURL: 'https://cdn.discordapp.com/attachments/1067536544346415144/1214712420606672896/srcn.jpg?ex=65fa1c19&is=65e7a719&hm=c974d4b645aca241164013f40292e562d89e1ffa1d6e4b9ba262daa058ac1aa5&'
        };
    }

    getAppOdds() {
        const datawisePick = this.doc.datawise_pick;

        if (datawisePick === 'Over') {
            return this.doc.over_odds_american;

        } else if (datawisePick === 'Under') {
            return this.doc.under_odds_american;

        } else {
            return null;
        }
    }
}

class Oaklawn extends Collection {
    getFirstFieldName() {
        return `Kambi EV`;
    }

    getAuthor() {
        return {
            name: 'Kambi',
            iconURL: 'https://cdn.discordapp.com/attachments/1196675146379960320/1215669307774861372/kambisports_logo.jpg?ex=65fd9745&is=65eb2245&hm=0695b89daadd821461438c2f809bc6efbfe3a0ef3c2a46aaebc3adb80b45bc88&'
        };
    }

    getAppOdds() {
        const datawisePick = this.doc.datawise_pick;

        if (datawisePick === 'Over') {
            return this.doc.over_odds_american;

        } else if (datawisePick === 'Under') {
            return this.doc.under_odds_american;

        } else {
            return null;
        }
    }
}

class BetOnline extends Collection {
    getAuthor() {
        return {
            name: 'BetOnline',
            iconURL: 'https://cdn.discordapp.com/attachments/1067536544346415144/1210591625605021756/betonline.jpg?ex=65eb1e4f&is=65d8a94f&hm=7d6605c6ff441adea8854005e8ba9bceeb88c741ebafdc63d00c437d7265f61e&'
        };
    }

    getAppOdds() {
        const datawisePick = this.doc.datawise_pick;

        if (datawisePick === 'Over') {
            return this.doc.over_odds_american;

        } else if (datawisePick === 'Under') {
            return this.doc.under_odds_american;

        } else {
            return null;
        }
    }
}

class Betr extends Collection {
    getAuthor() {
        return {
            name: 'Betr',
            iconURL: 'https://cdn.discordapp.com/attachments/1067536544346415144/1167453066635780106/betr.jpg?ex=65858d65&is=65731865&hm=69dd4188b7ac4a709f26ba4674db95739eae8f43d6cf572b186517b16bef27e6&'
        };
    }
}

class BetRivers extends Collection {
    getAuthor() {
        return {
            name: 'BetRivers',
            iconURL: 'https://cdn.discordapp.com/attachments/1067536544346415144/1209960395423617064/betriversjpg.jpg?ex=65e8d26e&is=65d65d6e&hm=8482911750eb1e1410152f6475631d5cfc1eccaf3150228bf14d56fcbc2b9964&'
        };
    }

    getAppOdds() {
        const datawisePick = this.doc.datawise_pick;

        if (datawisePick === 'Over') {
            return this.doc.over_odds_american;

        } else if (datawisePick === 'Under') {
            return this.doc.under_odds_american;

        } else {
            return null;
        }
    }
}

class Boom extends Collection {
    getAuthor() {
        return {
            name: 'Boom Fantasy',
            iconURL: 'https://cdn.discordapp.com/attachments/1067536544346415144/1185752967001944154/boom.jpg?ex=6590c181&is=657e4c81&hm=63a84ec93eabef32c5de1fa7181bc11ae4517362c59065867ee0f76d691fe159&'
        };
    }

}

class Bovada extends Collection {
    getAuthor() {
        return {
            name: 'Bovada',
            iconURL: 'https://cdn.discordapp.com/attachments/1196675146379960320/1199095224258019450/bovada.jpg?ex=65c14b73&is=65aed673&hm=c5bea980fb632d110f7c22a2e4e4ef2074e60f4146877541549f90ed610ae349&'
        };
    }

    getAppOdds() {
        let appOdds;
        const datawisePick = this.doc.datawise_pick;

        if (datawisePick === 'Over') {
            appOdds = this.doc.over_odds_american;
            return appOdds;

        } else if (datawisePick === 'Under') {
            appOdds = this.doc.under_odds_american;
            return appOdds;
        }
    }
}

class Caesars extends Collection {
    getAuthor() {
        return {
            name: 'Caesars Sportsbook',
            iconURL: 'https://cdn.discordapp.com/attachments/1067536544346415144/1192552253035384993/icon-paddypower-1.png?ex=65a97dd5&is=659708d5&hm=153c38e80e6a7c7c966adba041ae1bb69217938a8fd96592f0a2a46cfee58005&'
        };
    }

    getAppOdds() {
        const datawisePick = this.doc.datawise_pick;

        if (datawisePick === 'Over') {
            return this.doc.over_odds_american;

        } else if (datawisePick === 'Under') {
            return this.doc.under_odds_american;

        } else {
            return null;
        }
    }
}

class Chalkboard extends Collection {
    getDescription() {
        return null;
    }

    getAuthor() {
        return {
            name: 'Chalkboard',
            iconURL: 'https://cdn.discordapp.com/attachments/1067536544346415144/1182434444238409959/chalkboard.png?ex=6584aee4&is=657239e4&hm=ada5b2623b79324d769bbccd952fe0bb3dd21d265fb2366c2e25c78d0a46fc49&'
        };
    }

    getMulti() {
        let multi;
        const datawisePick = this.doc.datawise_pick;

        if (datawisePick === 'Over') {
            multi = this.doc.over_multi;
            return multi;

        } else if (datawisePick === 'Under') {
            multi = this.doc.under_multi;
            return multi;
        }
    }

    getAppOdds() {
        const datawisePick = this.doc.datawise_pick;

        if (datawisePick === 'Over') {
            return this.doc.over_odds_american;

        } else if (datawisePick === 'Under') {
            return this.doc.under_odds_american;

        } else {
            return null;
        }
    }
}

class Dabble extends Collection {
    getAuthor() {
        return {
            name: 'Dabble',
            iconURL: 'https://cdn.discordapp.com/attachments/1067536544346415144/1186726452440338503/dabble.jpg?ex=65944c22&is=6581d722&hm=d7f0234bc40c8d68b18df54ebd32655555821b29802ac209abfbdd3d2767abbd&'
        };
    }

}

class ESPNBet extends Collection {
    getAuthor() {
        return {
            name: 'ESPNBet',
            iconURL: 'https://cdn.discordapp.com/attachments/1067536544346415144/1200581460915134605/espnbet.jpg?ex=65c6b39e&is=65b43e9e&hm=ded1c8f24f1d96a9155604b5a201085d6b0d0e55b8da31d78448a5cc9347234e&'
        };
    }


    getAppOdds() {
        let appOdds;
        const datawisePick = this.doc.datawise_pick;

        if (datawisePick === 'Over') {
            appOdds = this.doc.over_odds_american;
            return appOdds;

        } else if (datawisePick === 'Under') {
            appOdds = this.doc.under_odds_american;
            return appOdds;
        }
    }
}

class Fanatics extends Collection {
    getAuthor() {
        return {
            name: 'Fanatics',
            iconURL: 'https://cdn.discordapp.com/attachments/1067536544346415144/1225512617322348676/15588daf-bd92-44db-865b-cecc0f23d003.jpg?ex=66216690&is=660ef190&hm=16a12ff7ea3ca20a59b67e036309a4729d6377b4e9c8363aa3c54edeca9cb22d&'
        };
    }

    getAppOdds() {
        const datawisePick = this.doc.datawise_pick;

        if (datawisePick === 'Over') {
            return this.doc.over_odds_american;

        } else if (datawisePick === 'Under') {
            return this.doc.under_odds_american;

        } else {
            return null;
        }
    }
}

class Fanduel extends Collection {
    getAuthor() {
        return {
            name: 'Fanduel',
            iconURL: 'https://github.com/tvnner2/sportsbook-images/blob/main/assets/Fanduel.png?raw=true'
        };
    }

    getAppOdds() {
        const datawisePick = this.doc.datawise_pick;

        if (datawisePick === 'Over') {
            return this.doc.over_odds_american;

        } else if (datawisePick === 'Under') {
            return this.doc.under_odds_american;

        } else {
            return null;
        }
    }
}

class Fliff extends Collection {

    getAuthor() {
        return {
            name: 'Fliff',
            iconURL: 'https://cdn.discordapp.com/attachments/1067536544346415144/1180164799666073630/fliff-logo.png?ex=658ee21e&is=657c6d1e&hm=ee3da540574a4f32f8df788a2aaef61a0fd824b31e39aa6019b965fad6fe12bc&'
        };
    }


    getAppOdds() {
        let appOdds;
        const datawisePick = this.doc.datawise_pick;

        if (datawisePick === 'Over') {
            appOdds = this.doc.over_odds_american;
            return appOdds;

        } else if (datawisePick === 'Under') {
            appOdds = this.doc.under_odds_american;
            return appOdds;
        }
    }
}

class Hardrock extends Collection {
    getAuthor() {
        return {
            name: 'Hard Rock Bet',
            iconURL: 'https://cdn.discordapp.com/attachments/1067536544346415144/1192477961673261106/fJyTgiyO_400x400.png?ex=65a938a5&is=6596c3a5&hm=879a3e49bf53102208407a6ab66a25f09fb8e606fedec8ae29923af0d29049b0&'
        };
    }

    getAppOdds() {
        let appOdds;
        const datawisePick = this.doc.datawise_pick;

        if (datawisePick === 'Over') {
            appOdds = this.doc.odds_over_american;
            return appOdds;

        } else if (datawisePick === 'Under') {
            appOdds = this.doc.odds_under_american;
            return appOdds;
        }
    }
}

class Hotstreak extends Collection {
    getDescription() {
        return null;
    }

    getAuthor() {
        return {
            name: 'Hotstreak',
            iconURL: 'https://cdn.discordapp.com/attachments/1067536544346415144/1151180232393490524/hotsreak.jpg?ex=658af3a3&is=65787ea3&hm=d3c40e644ea83353b70967e08f7d0137db6560d1940f37733d155a1ec9dcbecb&'
        };
    }

    getMulti() {
        let multi;
        const datawisePick = this.doc.datawise_pick;

        if (datawisePick === 'Over') {
            multi = this.doc.over_odds_decimal.toFixed(2) + 'x';
            return multi;

        } else if (datawisePick === 'Under') {
            multi = this.doc.under_odds_decimal.toFixed(2) + 'x';
            return multi;
        }
    }

    getAppOdds() {
        let appOdds;
        const datawisePick = this.doc.datawise_pick;

        if (datawisePick === 'Over') {
            appOdds = this.doc.over_odds_american;
            return appOdds;

        } else if (datawisePick === 'Under') {
            appOdds = this.doc.under_odds_american;
            return appOdds;
        }
    }
}

class JockMKT extends Collection {
    getAuthor() {
        return {
            name: 'JockMKT',
            iconURL: 'https://cdn.discordapp.com/attachments/1196675146379960320/1199415306527969301/jock.jpg?ex=65c2758d&is=65b0008d&hm=b7c7ea9401893a74a1665d6e5a0dd50d2a4cb76fe418b8d2065930c19df6a3ba&'
        };
    }
}

class Novig extends Collection {
    getAuthor() {
        return {
            name: 'Novig',
            iconURL: 'https://github.com/tvnner2/sportsbook-images/blob/main/assets/novig.png?raw=true'
        };
    }

    getAppOdds() {
        const datawisePick = this.doc.datawise_pick;

        if (datawisePick === 'Over') {
            return this.doc.over_odds_american;

        } else if (datawisePick === 'Under') {
            return this.doc.under_odds_american;

        } else {
            return null;
        }
    }
}

class ParlayPlay extends Collection {
    getDescription() {
        return null;
    }

    getAuthor() {
        return {
            name: 'ParlayPlay',
            iconURL: 'https://cdn.discordapp.com/attachments/1067536544346415144/1184252777363427418/oO-RNksh_400x400.jpg?ex=658b4c58&is=6578d758&hm=b488d64215a07e47e6958b5b2a8ee1d187140bff7a0cbae8d701cb62b008354b&'
        };
    }

    getMulti() {
        let multi;
        const datawisePick = this.doc.datawise_pick;

        if (datawisePick === 'Over') {
            multi = this.doc.over_odds_decimal.toFixed(2) + 'x';
            return multi;

        } else if (datawisePick === 'Under') {
            multi = this.doc.under_odds_decimal.toFixed(2) + 'x';
            return multi;
        }
    }

    getAppOdds() {
        let appOdds;
        const datawisePick = this.doc.datawise_pick;

        if (datawisePick === 'Over') {
            appOdds = this.doc.over_odds_american;
            return appOdds;

        } else if (datawisePick === 'Under') {
            appOdds = this.doc.under_odds_american;
            return appOdds;
        }
    }
}

class Pick6 extends Collection {
    getAuthor() {
        return {
            name: 'Pick6',
            iconURL: 'https://cdn.discordapp.com/attachments/1196675146379960320/1199415328099274772/pick.jpg?ex=65c27592&is=65b00092&hm=4b913f57ac82a55714f6842a4e5beb5088b08fef8c344a6363879b398522d3e1&'
        };
    }
}

class PrizePicks extends Collection {
    getAuthor() {
        return {
            name: 'PrizePicks',
            iconURL: 'https://github.com/tvnner2/sportsbook-images/blob/main/assets/PrizePicks.png?raw=true'
        };
    }
}

class Rebet extends Collection {
    getAuthor() {
        return {
            name: 'Rebet',
            iconURL: 'https://github.com/tvnner2/sportsbook-images/blob/main/assets/rebet.png?raw=true'
        };
    }

    getAppOdds() {
        const datawisePick = this.doc.datawise_pick;

        if (datawisePick === 'Over') {
            return this.doc.over_odds_american;

        } else if (datawisePick === 'Under') {
            return this.doc.under_odds_american;

        } else {
            return null;
        }
    }
}

class Sleeper extends Collection {
    getDescription() {
        return null;
    }

    getAuthor() {
        return {
            name: 'Sleeper',
            iconURL: 'https://cdn.discordapp.com/attachments/1067536544346415144/1185337584591847486/logo-mobile.png?ex=658f3ea6&is=657cc9a6&hm=44a24404818e047c999065330b69c99c46c47d00a74170d8cfef9abdcc77c209&'
        };
    }

    getMulti() {
        let multi;
        const datawisePick = this.doc.datawise_pick;

        if (datawisePick === 'Over') {
            multi = this.doc.over_odds_decimal + 'x';
            return multi;

        } else if (datawisePick === 'Under') {
            multi = this.doc.under_odds_decimal + 'x';
            return multi;
        }
    }

    getAppOdds() {
        let appOdds;
        const datawisePick = this.doc.datawise_pick;

        if (datawisePick === 'Over') {
            appOdds = this.doc.over_odds_american;
            return appOdds;

        } else if (datawisePick === 'Under') {
            appOdds = this.doc.under_odds_american;
            return appOdds;
        }
    }
}

class SportsBattle extends Collection {
    getFirstFieldName() {
        return `SportsQuack EV`;
    }

    getAuthor() {
        return {
            name: 'SportsQuack',
            iconURL: 'https://cdn.discordapp.com/attachments/1067536544346415144/1201245823506333767/fK_cfR2T_400x400.jpg?ex=65c91e5a&is=65b6a95a&hm=fa968482741626a1f167b575022aca3fbf91248fbc0836f126690030bad2e3b0&'
        };
    }
}

class Underdog extends Collection {
    getDescription() {
        return null;
    }

    getAuthor() {
        return {
            name: 'Underdog Fantasy',
            iconURL: 'https://cdn.discordapp.com/attachments/1067536544346415144/1152249765468324000/217x0w_1.png?ex=654503b7&is=65328eb7&hm=18c64f510e3ea763cba3914ec32619b7e091661a960631bf4b42a1f662492398'
        };
    }

    getMulti() {
        let multi;
        const datawisePick = this.doc.datawise_pick;

        if (datawisePick === 'Over') {
          if (this.doc.over_odds_decimal === 1.86) {
            multi = '1.0x';
          } else {
            multi = (this.doc.over_odds_decimal / 1.86).toFixed(2) + 'x';
          }
          return multi;
        } else if (datawisePick === 'Under') {
          if (this.doc.under_odds_decimal === 1.86) {
            multi = '1.0x';
          } else {
            multi = (this.doc.under_odds_decimal / 1.86).toFixed(2) + 'x';
          }
          return multi;
        }
      }

    getAppOdds() {
        let appOdds;
        const datawisePick = this.doc.datawise_pick;

        if (datawisePick === 'Over') {
            appOdds = this.doc.over_odds_american;
            return appOdds;

        } else if (datawisePick === 'Under') {
            appOdds = this.doc.under_odds_american;
            return appOdds;
        }
    }
}

class Vivid extends Collection {
    getAuthor() {
        return {
            name: 'Vivid Picks',
            iconURL: 'https://cdn.discordapp.com/attachments/1067536544346415144/1187485108974919801/vivid.jpg?ex=65970eb0&is=658499b0&hm=b742781598489a1337462b9413a465609e5b6cec43af5899bfb9e55e34218837&'
        };
    }
}

class OwnersBox extends Collection {
    getAuthor() {
        return {
            name: 'OwnersBox',
            iconURL: 'https://github.com/datawisebets/logos/blob/ae96b5ce9c8a1b46e5d810df128cb53e66a7e1ad/ownersbox.png?raw=true'
        };
    }
}

class PropBuilder extends Collection {
    getFirstFieldName() {
        return `PropBuilder EV`;
    }

    getAuthor() {
        return {
            name: 'PropBuilder',
            iconURL: 'https://cdn.discordapp.com/attachments/1067536544346415144/1210591625605021756/betonline.jpg?ex=65eb1e4f&is=65d8a94f&hm=7d6605c6ff441adea8854005e8ba9bceeb88c741ebafdc63d00c437d7265f61e&'
        };
    }

    getAppOdds() {
        const datawisePick = this.doc.datawise_pick;

        if (datawisePick === 'Over') {
            return this.doc.over_odds_american;
        } else if (datawisePick === 'Under') {
            return this.doc.under_odds_american;
        } else {
            return null;
        }
    }
}

class DraftKings extends Collection {
    getAuthor() {
        return {
            name: 'DraftKings',
            iconURL: 'https://github.com/tvnner2/sportsbook-images/blob/main/assets/DraftKings.png?raw=true'
        };
    }

    getAppOdds() {
        const datawisePick = this.doc.datawise_pick;

        if (datawisePick === 'Over') {
            return this.doc.over_odds_american;

        } else if (datawisePick === 'Under') {
            return this.doc.under_odds_american;

        } else {
            return null;
        }
    }
}

class ProphetX extends Collection {
    getAuthor() {
        return {
            name: 'ProphetX',
            iconURL: process.env.PROPHETX_IMG // Use the env variable for the icon URL
        };
    }

    getAppOdds() {
        const datawisePick = this.doc.datawise_pick;

        if (datawisePick === 'Over') {
            return this.doc.over_odds_american;

        } else if (datawisePick === 'Under') {
            return this.doc.under_odds_american;

        } else {
            return null;
        }
    }
}

// Added Epick Class
class Epick extends Collection {
    getAuthor() {
        return {
            name: 'Epick',
            iconURL: 'https://github.com/datawisebets/logos/blob/f5ed14fc4dda9c958e25611848e197806a5de786/epick.png?raw=true' // Use the provided URL
        };
    }
}


const collectionMap = {
    'betsaracen': BetSaracen,
    'oaklawn': Oaklawn,
    'betmgm': BetMGM,
    'betonline': PropBuilder, // Assuming BetOnline uses PropBuilder logic based on previous code
    'betr': Betr,
    'betrivers': BetRivers,
    'boom': Boom,
    'bovada': Bovada,
    'caesars': Caesars,
    'chalkboard': Chalkboard,
    'dabble': Dabble,
    'draftkings': DraftKings,
    'espnbet': ESPNBet,
    'fanatics': Fanatics,
    'fanduel': Fanduel,
    'fliff': Fliff,
    'hardrock': Hardrock,
    'hotstreak': Hotstreak,
    'jockmkt': JockMKT,
    'novig': Novig,
    'parlayplay': ParlayPlay,
    'pick6': Pick6,
    'prizepicks': PrizePicks,
    'propbuilder': PropBuilder,
    'rebet': Rebet,
    'sleeper': Sleeper,
    'sportsbattle': SportsBattle,
    'underdog': Underdog,
    'vivid': Vivid,
    'ownersbox': OwnersBox,
    'prophetx': ProphetX,
    'epick': Epick // Added Epick to map
};

module.exports = { collectionMap };