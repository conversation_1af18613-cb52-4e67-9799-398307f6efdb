const { MongoClient } = require('mongodb');
const { url, dbName, collections } = require('../config/dbConfig');

const client = new MongoClient(url);

// Return the cursor instead of calling .toArray()
async function getDocumentsCursor(collectionName) {
  try {
    const actualCollectionName = collections[collectionName];
    if (!actualCollectionName) {
      console.error(`Error: No collection name found in dbConfig for key '${collectionName}'. Check environment variable COLLECTION_${collectionName.toUpperCase()}`);
      throw new Error(`Configuration error: Missing collection name for ${collectionName}`);
    }
    console.log(`Getting cursor for collection: ${actualCollectionName}`);

    const db = client.db(dbName);
    const collection = db.collection(actualCollectionName);

    // Return the cursor itself
    return collection.find({});

  } catch (err) {
    console.error(`Error getting cursor for ${collections[collectionName] || 'undefined collection'}:`, err);
    throw err;
  }
}

// Keep the client export if needed elsewhere, or manage connection in main.js
module.exports = { client, getDocumentsCursor };
