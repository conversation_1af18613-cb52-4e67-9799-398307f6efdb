// Load environment variables from .env
require('dotenv').config();

// Import required modules and functions
const { client, getDocumentsCursor } = require('./database/dbClient');
const { sendEmbeds } = require('./discord/discordSender');

// Initialize a Map to store documents that have already been sent
const sentDocuments = new Map();

// Cooldown period in milliseconds (5 minutes)
const COOLDOWN_PERIOD = 5 * 60 * 1000;


// Define an asynchronous function to process a collection of documents
async function processCollection(collectionName) {
  let cursor; // Define cursor variable outside try block for finally
  try {
    // Log the start of processing for a specific collection
    console.log(`Processing collection: ${collectionName}`);

    // Get the cursor from the database
    cursor = await getDocumentsCursor(collectionName); // Get cursor

    // Store the current time
    const currentTime = Date.now();

    // Define setModelEv function to take a document as a parameter
    function setModelEv(doc) {
      if (doc.sharp_ev !== null) {
        return doc.sharp_ev;
      } else {
        return doc.avg_ev;
      }
    }

    const documentsToSend = []; // Array to collect documents that pass filters

    // Iterate over the cursor using async for...of
    for await (const doc of cursor) {
       // --- Start processing logic for a single document ---
       if (!doc.sharps) {
         console.warn(`Document ${doc.proj_id || 'N/A'} in ${collectionName} missing 'sharps' array. Skipping.`);
         continue; // Skip if sharps are missing
       }
       if (!Array.isArray(doc.sharps)) {
            console.warn(`Document ${doc.proj_id || 'N/A'} in ${collectionName} has invalid 'sharps' (not an array). Skipping.`);
            continue; // Skip if sharps is not an array
       }

       // Calculate the expected value (modelEv) and convert it to a percentage
       const modelEv = setModelEv(doc);

       // Ensure doc.avg_ev is treated as a float and check if it's greater than the threshold
       const avgEv = parseFloat(doc.avg_ev);
       if (isNaN(avgEv) || avgEv <= 0.02) {
         continue; // Skip this document if avg_ev does not meet the threshold or is NaN
       }

       // Get the current sharp sources
       const currentSharpSources = doc.sharps.map(sharp => sharp.source).sort().join(',');

       // Determine the projection ID
       const projId = doc.proj_id ? doc.proj_id.toString() : 'unknown';
       if (projId === 'unknown') {
            console.warn(`Document in ${collectionName} missing 'proj_id'. Cannot track sent status reliably.`);
            // Decide whether to continue processing or skip
            // continue; // Option: skip docs without proj_id
       }


       // Create an object to store the current data
        const currentData = {
            modelEv,
            datawisePercent: parseFloat(doc.datawise_percent),
            timestamp: currentTime,
            sharpSources: currentSharpSources,
            player: doc.player || '',
            line: doc.line || '',
            matchup: doc.matchup || ''
       };

       // Check if this document has already been sent
       const alreadySent = sentDocuments.get(projId);
       if (alreadySent && projId !== 'unknown') { // Only check cooldown if we have a valid projId
         // Check if we're still in the cooldown period
         const timeSinceLastSent = currentTime - alreadySent.timestamp;
         if (timeSinceLastSent < COOLDOWN_PERIOD) {
           // Only send if there's a VERY significant change in EV (more than 10%)
           const evDifference = Math.abs(modelEv - alreadySent.modelEv);
           if (evDifference <= 0.10) {
             continue; // Skip if change isn't significant enough during cooldown
           }
         }

          // Outside cooldown period, check for significant changes
          // If player, line, and matchup are all the same, use a higher threshold
         if (alreadySent.player === currentData.player &&
             alreadySent.line === currentData.line &&
             alreadySent.matchup === currentData.matchup) {
           const evDifference = Math.abs(modelEv - alreadySent.modelEv);
           if (evDifference <= 0.07) {
             continue; // Skip if change isn't significant enough for identical plays
           }
         }
       }

       // If the expected value is significant (modelEv >= 0.02) and avg_ev meets the threshold, mark the document to be sent
       if (!isNaN(modelEv) && modelEv >= 0.02) {
         if (projId !== 'unknown') { // Only update sentDocuments if we have a valid projId
            sentDocuments.set(projId, currentData);
         }
         console.log(`Queueing projection ${projId} - EV: ${modelEv.toFixed(2)}, Sharp sources: ${currentSharpSources}`);
         documentsToSend.push(doc); // Add the doc to the list to send
       } else if (isNaN(modelEv)) {
            console.warn(`Calculated modelEv is NaN for doc ${projId} in ${collectionName}. Skipping send.`);
       }
      // --- End processing logic for a single document ---
    } // End of cursor iteration

    // If there are documents to send, send them
    if (documentsToSend.length > 0) {
      await sendEmbeds(documentsToSend, collectionName);
      console.log(`Sent ${documentsToSend.length} documents from ${collectionName}`);
    } else {
      console.log(`No new or updated documents to send from ${collectionName}.`);
    }
  } catch (err) {
    // Log any errors that occur during the processing
    console.error(`An error occurred processing ${collectionName}:`, err);
  } finally {
      // Ensure the cursor is closed even if errors occur
      if (cursor) {
          try {
              await cursor.close();
              console.log(`Closed cursor for ${collectionName}`);
          } catch (closeErr) {
              console.error(`Error closing cursor for ${collectionName}:`, closeErr);
          }
      }
  }
}

// Define the main asynchronous function to run the application
async function main() {
  try {
    // Establish MongoDB connection
    await client.connect();
    console.log('Connected to MongoDB.');

    await Promise.allSettled([
      processCollection('betmgm'),
      processCollection('betonline'),
      processCollection('betr'),
      processCollection('betrivers'),
      //processCollection('boom'),
      processCollection('bovada'),
      //processCollection('chalkboard'),
      processCollection('dabble'),
      processCollection('draftkings'),
      //processCollection('espnbet'),
      processCollection('fanatics'),
      processCollection('fanduel'),
      processCollection('fliff'),
      processCollection('hardrock'),
      processCollection('hotstreak'),
      processCollection('novig'),
      //processCollection('jockmkt'),
      //processCollection('parlayplay'),
      processCollection('ownersbox'),
      processCollection('pick6'),
      processCollection('prizepicks'),
      processCollection('propbuilder'),
      processCollection('rebet'),
      processCollection('sleeper'),
      //processCollection('sportsbattle'),
      processCollection('underdog'),
      processCollection('vivid'),
      processCollection('caesars'),
      processCollection('prophetx'),
      processCollection('epick'), // Added Epick


      /*
      processCollection('oaklawn'), // KAMBI
      processCollection('betsaracen'),

      */
    ]);

    console.log('All collections processed. Waiting for next run...');
  } catch (err) {
    console.error('An error occurred:', err);
  } finally {
    // Close MongoDB connection
    if (client && client.topology && client.topology.isConnected()) {
        await client.close();
        console.log('Disconnected from MongoDB.');
    } else {
        console.log('MongoDB client already closed or not connected.');
    }


    setTimeout(main, 500);
  }
}

// Immediately invoke the main function to start the application
main();