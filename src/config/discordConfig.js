// ./src/config/discordConfig

module.exports = {
  webhookUrls: {
    betmgm: process.env.BETMGM,
    betonline: process.env.BETONLINE,
    betsaracen: process.env.BETSARACEN,
    oaklawn: process.env.OAKLAWN,
    betr: process.env.BETR,
    betrivers: process.env.BETRIVERS,
    boom: process.env.BOOM,
    bovada: process.env.BOVADA,
    chalkboard: process.env.CHALKBOARD,
    dabble: process.env.DABBLE,
    draftkings: process.env.DRAFTKINGS,
    espnbet: process.env.ESPNBET,
    fanatics: process.env.FANATICS,
    fanduel: process.env.FANDUEL,
    fliff: process.env.FLIFF,
    hardrock: process.env.HARDROCK,
    hotstreak: process.env.HOTSTREAK,
    jockmkt: process.env.JOCKMKT,
    novig: process.env.NOVIG,
    ownersbox: process.env.OWNERSBOX,
    parlayplay: process.env.PARLAYPLAY,
    pick6: process.env.PICK6,
    prizepicks: process.env.PRIZEPICKS,
    propbuilder: process.env.PROPBUILDER,
    rebet: process.env.REBET,
    sleeper: process.env.SLEEPER,
    sportsbattle: process.env.SPORTSBATTLE,
    underdog: process.env.UNDERDOG,
    vivid: process.env.VIVID,
    caesars: process.env.CAESARS,
    prophetx: process.env.PROPHETX,
    epick: process.env.EPICK // Added Epick webhook URL
  },
  allDfsWebhookUrl: process.env.ALL_DFS,
  allSportsbookWebhookUrl: process.env.ALL_SPORTSBOOK,
  highEvWebhookDFS: process.env.HIGH_EV_DFS,
  highEvWebhookSportsbooks: process.env.HIGH_EV_SPORTSBOOKS,
  hammerWebhookDFS: process.env.HAMMER_DFS,
  hammerWebhookSportsbooks: process.env.HAMMER_SPORTSBOOKS,
  longshotWebhook: process.env.LONGSHOT,
  roleMap: {
    betmgm: process.env.BETMGM_ROLE_ID,
    betonline: process.env.BETONLINE_ROLE_ID,
    betsaracen: process.env.BETSARACEN_ROLE_ID,
    oaklawn: process.env.OAKLAWN_ROLE_ID,
    betr: process.env.BETR_ROLE_ID,
    betrivers: process.env.BETRIVERS_ROLE_ID,
    boom: process.env.BOOM_ROLE_ID,
    bovada: process.env.BOVADA_ROLE_ID,
    chalkboard: process.env.CHALKBOARD_ROLE_ID,
    dabble: process.env.DABBLE_ROLE_ID,
    espnbet: process.env.ESPNBET_ROLE_ID,
    fanatics: process.env.FANATICS_ROLE_ID,
    fanduel: process.env.FANDUEL_ROLE_ID,
    fliff: process.env.FLIFF_ROLE_ID,
    hardrock: process.env.HARDROCK_ROLE_ID,
    hotstreak: process.env.HOTSTREAK_ROLE_ID,
    jockmkt: process.env.JOCKMKT_ROLE_ID,
    novig: process.env.NOVIG_ROLE_ID,
    ownersbox: process.env.OWNERSBOX_ROLE_ID,
    parlayplay: process.env.PARLAYPLAY_ROLE_ID,
    pick6: process.env.PICK6_ROLE_ID,
    prizepicks: process.env.PRIZEPICKS_ROLE_ID,
    propbuilder: process.env.PROPBUILDER_ROLE_ID,
    rebet: process.env.REBET_ROLE_ID,
    sleeper: process.env.SLEEPER_ROLE_ID,
    sportsbattle: process.env.SPORTSBATTLE_ROLE_ID,
    underdog: process.env.UNDERDOG_ROLE_ID,
    vivid: process.env.VIVID_ROLE_ID,
    caesars: process.env.CAESARS_ROLE_ID,
    prophetx: process.env.PROPHETX_ROLE_ID,
    epick: process.env.EPICK_ROLE_ID // Added Epick role ID
  },
  dfsHighEvRoleMap: {
      prizepicks: process.env.PRIZEPICKS_HIGH_EV_ROLE_ID,
      underdog: process.env.UNDERDOG_HIGH_EV_ROLE_ID,
      betr: process.env.BETR_HIGH_EV_ROLE_ID,
      boom: process.env.BOOM_HIGH_EV_ROLE_ID,
      chalkboard: process.env.CHALKBOARD_HIGH_EV_ROLE_ID,
      dabble: process.env.DABBLE_HIGH_EV_ROLE_ID,
      hotstreak: process.env.HOTSTREAK_HIGH_EV_ROLE_ID,
      jockmkt: process.env.JOCKMKT_HIGH_EV_ROLE_ID,
      ownersbox: process.env.OWNERSBOX_HIGH_EV_ROLE_ID,
      parlayplay: process.env.PARLAYPLAY_HIGH_EV_ROLE_ID,
      sleeper: process.env.SLEEPER_HIGH_EV_ROLE_ID,
      sportsbattle: process.env.SPORTSBATTLE_HIGH_EV_ROLE_ID,
      vivid: process.env.VIVID_HIGH_EV_ROLE_ID
      // Note: 'pick6' and 'epick' are intentionally excluded as they don't have High EV channels/roles currently
  },
  sportsbookHighEvRoleMap: {
    betmgm: process.env.BETMGM_HIGH_EV_ROLE_ID,
    betonline: process.env.BETONLINE_HIGH_EV_ROLE_ID,
    betrivers: process.env.BETRIVERS_HIGH_EV_ROLE_ID,
    bovada: process.env.BOVADA_HIGH_EV_ROLE_ID,
    caesars: process.env.CAESARS_HIGH_EV_ROLE_ID,
    draftkings: process.env.DRAFTKINGS_HIGH_EV_ROLE_ID,
    espnbet: process.env.ESPNBET_HIGH_EV_ROLE_ID,
    fanatics: process.env.FANATICS_HIGH_EV_ROLE_ID,
    fanduel: process.env.FANDUEL_HIGH_EV_ROLE_ID,
    fliff: process.env.FLIFF_HIGH_EV_ROLE_ID,
    hardrock: process.env.HARDROCK_HIGH_EV_ROLE_ID,
    novig: process.env.NOVIG_HIGH_EV_ROLE_ID,
    propbuilder: process.env.PROPBUILDER_HIGH_EV_ROLE_ID,
    rebet: process.env.REBET_HIGH_EV_ROLE_ID,
    betsaracen: process.env.BETSARACEN_HIGH_EV_ROLE_ID,
    oaklawn: process.env.OAKLAWN_HIGH_EV_ROLE_ID,
    prophetx: process.env.PROPHETX_HIGH_EV_ROLE_ID
}
};