module.exports = {
  url: process.env.DB_URL,
  dbName: process.env.DB_NAME,
  collections: {
    betmgm: process.env.COLLECTION_BETMGM,
    betonline: process.env.COLLECTION_BETONLINE,
    betsaracen: process.env.COLLECTION_BETSARACEN,
    oaklawn: process.env.COLLECTION_OAKLAWN,
    betr: process.env.COLLECTION_BETR,
    betrivers: process.env.COLLECTION_BETRIVERS,
    boom: process.env.COLLECTION_BOOM,
    bovada: process.env.COLLECTION_BOVADA,
    chalkboard: process.env.COLLECTION_CHALKBOARD,
    dabble: process.env.COLLECTION_DABBLE,
    draftkings: process.env.COLLECTION_DRAFTKINGS,
    espnbet: process.env.COLLECTION_ESPNBET,
    fanatics: process.env.COLLECTION_FANATICS,
    fanduel: process.env.COLLECTION_FANDUEL,
    fliff: process.env.COLLECTION_FLIFF,
    hardrock: process.env.COLLECTION_HARDROCK,
    hotstreak: process.env.COLLECTION_HOTSTREAK,
    jockmkt: process.env.COLLECTION_JOCKMKT,
    novig: process.env.COLLECTION_NOVIG,
    ownersbox: process.env.COLLECTION_OWNERSBOX,
    parlayplay: process.env.COLLECTION_PARLAYPLAY,
    pick6: process.env.COLLECTION_PICK6,
    prizepicks: process.env.COLLECTION_PRIZEPICKS,
    propbuilder: process.env.COLLECTION_PROPBUILDER,
    rebet: process.env.COLLECTION_REBET,
    sleeper: process.env.COLLECTION_SLEEPER,
    sportsbattle: process.env.COLLECTION_SPORTSBATTLE,
    underdog: process.env.COLLECTION_UNDERDOG,
    vivid: process.env.COLLECTION_VIVID,
    caesars: process.env.COLLECTION_CAESARS,
    prophetx: process.env.COLLECTION_PROPHETX,
    epick: process.env.COLLECTION_EPICK // Added Epick
  }
};