// Collection type definitions
const dfsCollections = ['betr', 'boom', 'chalkboard', 'dabble', 'hotstreak', 'jockmkt', 'parlayplay', 'prizepicks', 'sleeper', 'sportsbattle', 'vivid', 'underdog', 'pick6', 'ownersbox', 'epick'];
const sportsbookCollections = ['betmgm', 'betrivers', 'bovada', 'caesars', 'espnbet', 'fliff', 'hardrock', 'betsaracen', 'oaklawn', 'fanatics', 'fanduel', 'rebet', 'novig', 'propbuilder', 'draftkings', 'prophetx'];

/**
 * Validates if a collection name is supported by the system
 * @param {string} collectionName - The name of the collection to validate
 * @returns {boolean} - Whether the collection name is valid
 */
function isCollectionNameValid(collectionName) {
    return [...dfsCollections, ...sportsbookCollections].includes(collectionName);
}

module.exports = {
    dfsCollections,
    sportsbookCollections,
    isCollectionNameValid
}; 