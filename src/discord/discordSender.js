const { EmbedBuilder } = require('discord.js');
const {
    webhookUrls,
    allDfsWebhookUrl,
    allSportsbookWebhookUrl,
    highEvWebhookDFS,
    highEvWebhookSportsbooks,
    hammerWebhookDFS,
    hammerWebhookSportsbooks,
    longshotWebhook
} = require('../config/discordConfig');
const { collectionMap } = require('../database/collections');
const { dfsCollections, sportsbookCollections, isCollectionNameValid } = require('../config/collections');
const { getDocMetrics, shouldSkipDoc } = require('./documentProcessing');
const { validateSharpBooks, processHighEvPlays, processHammerPlays, processLongshotPlays } = require('./playProcessing');
const { safeWebhookClient, sendEmbedsSequentially } = require('./webhookUtils');
const { formatOdds } = require('../config/utils'); // formatOdds might be unused now, double check

/**
 * Main function to process and send embeds to Discord for betting opportunities
 * @param {Array} documents - Array of betting documents to process
 * @param {string} collectionName - Name of the collection being processed
 */
async function sendEmbeds(documents, collectionName) {
    // Get the webhook URL specific to this collection
    const collectionWebhookUrl = webhookUrls[collectionName];
    if (!collectionWebhookUrl) {
        console.error('No specific webhook URL found for collection:', collectionName);
        return;
    }

    // Initialize all webhook clients needed for different types of notifications
    const highEvHookDFS = safeWebhookClient(highEvWebhookDFS, collectionName);
    const highEvHookSportsbooks = safeWebhookClient(highEvWebhookSportsbooks, collectionName);
    const hammerHookDFS = safeWebhookClient(hammerWebhookDFS, collectionName);
    const hammerHookSportsbooks = safeWebhookClient(hammerWebhookSportsbooks, collectionName);
    const allDfsHook = safeWebhookClient(allDfsWebhookUrl, collectionName);
    const allSportsbookHook = safeWebhookClient(allSportsbookWebhookUrl, collectionName);
    const collectionHook = safeWebhookClient(collectionWebhookUrl, collectionName);
    const longshotHook = safeWebhookClient(longshotWebhook, collectionName);

    // Ensure essential webhooks are available before proceeding
    if (!collectionHook || !allDfsHook || !allSportsbookHook) {
        console.error(`Essential webhook clients for collection or all are not available.`);
        return;
    }

    // Initialize arrays to store different types of betting opportunities
    const embeds = [];  // Standard plays
    const highEvEmbedsDFS = [];  // High EV plays for DFS
    const highEvEmbedsSportsbooks = [];  // High EV plays for sportsbooks
    const hammerEmbedsDFS = [];  // Hammer plays for DFS (highest confidence)
    const hammerEmbedsSportsbooks = [];  // Hammer plays for sportsbooks
    const longshotEmbeds = [];  // Longshot opportunities

    // Determine if this collection is DFS or Sportsbook
    const isDfsCollection = dfsCollections.includes(collectionName);
    const isSportsbookCollection = sportsbookCollections.includes(collectionName);

    // Process each betting document
    for (const doc of documents) {
        // Get the appropriate collection class for this document
        const CollectionClass = collectionMap[collectionName];
        if (!CollectionClass) {
            console.error('Unknown collection:', collectionName);
            continue;
        }

        const collection = new CollectionClass(doc);

        // Extract metrics for filtering and later use
        const metrics = getDocMetrics(collection);
        if (shouldSkipDoc(metrics, doc, collectionName)) {
            continue;
        }

        // Destructure metrics for ease of use later in embed creation
        const { modelEv, tiePercent, datawisePercent, datawisePercentNumeric, avgEVRaw } = metrics;

        // --- Create Embed Title ---
        const playerName = doc.player_name;
        const statType = doc.stat_type;
        const line = doc.line;
        const datawisePick = doc.datawise_pick || 'N/A'; // Default if missing

        // Base title structure: Player - Pick Line Stat
        let title = `${playerName} - ${datawisePick} ${line} ${statType}`;

        // Add Underdog Multiplier if applicable
        if (collectionName.toLowerCase() === 'underdog') {
            // Use collection.getMulti() which returns the calculated decimal multiplier string
            const multiplierString = collection.getMulti();
            if (multiplierString) {
                 // Check if the multiplier is effectively 1x before adding
                 // Parse the numeric part (e.g., "0.94" from "0.94x")
                 const numericPart = parseFloat(multiplierString);

                 // Only add if it's not NaN and not equal to 1
                 if (!isNaN(numericPart) && numericPart !== 1) {
                    const multiplierValue = String(multiplierString).trim();
                    if (multiplierValue) {
                        title += ` ${multiplierValue}`;
                    }
                 }
            }
        }

        // Add Novig limit if applicable (Append to the new title structure)
        if (collectionName.toLowerCase() === 'novig' && datawisePick !== 'N/A') {
            let limit = null;
            const pickLower = datawisePick.toLowerCase();

            if (pickLower === 'over' && doc.novig_over_limit !== undefined && doc.novig_over_limit !== null) {
                limit = doc.novig_over_limit;
            } else if (pickLower === 'under' && doc.novig_under_limit !== undefined && doc.novig_under_limit !== null) {
                limit = doc.novig_under_limit;
            }

            if (limit !== null) {
                // Append Novig limit after potential Underdog multiplier
                title += ` (Limit: $${limit})`;
            }
        }

        // Add ProphetX limit if applicable (Append to the new title structure)
        if (collectionName.toLowerCase() === 'prophetx' && datawisePick !== 'N/A') {
            let limit = null;
            const pickLower = datawisePick.toLowerCase();

            // Check for limit based on the pick side
            if (pickLower === 'over' && doc.prophetx_over_limit !== undefined && doc.prophetx_over_limit !== null) {
                limit = doc.prophetx_over_limit;
            } else if (pickLower === 'under' && doc.prophetx_under_limit !== undefined && doc.prophetx_under_limit !== null) {
                limit = doc.prophetx_under_limit;
            }

            if (limit !== null) {
                // Append ProphetX limit
                title += ` (Limit: $${limit})`;
            }
        }
        // --- End Create Embed Title ---

        const embed = new EmbedBuilder()
            .setTitle(title) // Use the newly constructed title
            // Rely on collection methods for description, color, author etc.
            .setDescription(collection.getDescription())
            .setColor(collection.getColor())
            .setAuthor({ name: collection.getAuthor().name, iconURL: collection.getAuthor().iconURL })
            .setThumbnail("https://github.com/tvnner2/sportsbook-images/blob/main/assets/Logo_TransparentBackground.png?raw=true")
            .setFooter({ text: 'Powered by Datawise', iconURL: "https://github.com/tvnner2/sportsbook-images/blob/main/assets/Logo_TransparentBackground.png?raw=true" })
            .setTimestamp();

        let fieldsToAdd = [];

        // Get key information for the embed
        const firstFieldName = collection.getFirstFieldName();
        const matchup = collection.getMatchup();
        const sharpEV = collection.getSharpEV();
        const avgEV = collection.getAvgEV();

        // Special handling for Underdog based on their odds structure
        const isUnderdogWithDifferentOdds = collectionName === 'underdog' && (doc.over_odds_decimal !== 1.82 || doc.under_odds_decimal !== 1.82);
        const isUnderdogWithStandardOdds = collectionName === 'underdog' && (doc.over_odds_decimal === 1.82 || doc.under_odds_decimal === 1.82);

        // Handle Underdog bets with non-standard odds
        if (isUnderdogWithDifferentOdds) {
            const appOdds = collection.getAppOdds();
            let fieldContent;

            if (tiePercent > 0) {
                // Include tie percentage in the display
                fieldContent = `Odds: **${appOdds}**\nO: ${doc.model_over_percent}\nTie: ${tiePercent}%\nU: ${doc.model_under_percent}`;
            } else {
                // Show EV calculations without tie percentage
                fieldContent = `Odds: **${appOdds}**\nWin: ${datawisePercent}\n`;

                // Logic for displaying Sharp EV vs Average EV
                if (sharpEV === null) {
                    fieldContent += `Avg EV: ${avgEV}`;
                } else if (avgEV === sharpEV) {
                    fieldContent += `Sharp EV: ${sharpEV}`;
                } else {
                    fieldContent += `Sharp EV: ${sharpEV}\nAvg EV: ${avgEV}`;
                }
            }

            fieldsToAdd.push({ name: `${firstFieldName}`, value: fieldContent, inline: false });
        }

        // Handle Underdog bets with standard odds
        if (isUnderdogWithStandardOdds) {
            if (tiePercent > 0) {
                fieldsToAdd.push({ name: `${firstFieldName}`, value: `O: ${doc.model_over_percent}\nTie: ${tiePercent}%\nU: ${doc.model_under_percent}`, inline: false });
            } else {
                let fieldContent = `Win: ${datawisePercent}\n`;

                // Logic for displaying Sharp EV vs Average EV
                if (sharpEV === null) {
                    fieldContent += `Avg EV: ${avgEV}`;
                } else if (avgEV === sharpEV) {
                    fieldContent += `Sharp EV: ${sharpEV}`;
                } else {
                    fieldContent += `Sharp EV: ${sharpEV}\nAvg EV: ${avgEV}`;
                }

                fieldsToAdd.push({ name: `${firstFieldName}`, value: fieldContent, inline: false });
            }
        }

        // Handle apps with fixed odds (PrizePicks, Dabble, Boom, Betr, etc.)
        if (tiePercent > 0) {
            // For apps that need to show over/under percentages with ties
            // Added 'epick' here
            if (['prizepicks', 'dabble', 'boom', 'betr', 'sportsbattle', 'jockmkt', 'pick6', 'ownersbox', 'epick'].includes(collectionName)) {
                fieldsToAdd.push({ name: `${firstFieldName}`, value: `O: ${doc.model_over_percent}\nTie: ${tiePercent}%\nU: ${doc.model_under_percent}`, inline: false });
            }
        } else {
            // For apps that show win percentage and EV calculations
            // Added 'epick' here
            if (['prizepicks', 'vivid', 'dabble', 'boom', 'betr', 'sportsbattle', 'jockmkt', 'pick6', 'ownersbox', 'epick'].includes(collectionName)) {
                let fieldContent = `Win: ${datawisePercent}\n`;

                // Add appropriate EV display based on available data
                if (sharpEV === null) {
                    fieldContent += `Avg EV: ${avgEV}`;
                } else if (avgEV === sharpEV) {
                    fieldContent += `Sharp EV: ${sharpEV}`;
                } else {
                    fieldContent += `Sharp EV: ${sharpEV}\nAvg EV: ${avgEV}`;
                }

                fieldsToAdd.push({ name: `${firstFieldName}`, value: fieldContent, inline: false });
            }
        }

        // Handle apps with custom multipliers (Chalkboard, Hotstreak, Sleeper, ParlayPlay)
        if (tiePercent > 0) {
            if (['chalkboard', 'parlayplay', 'hotstreak', 'sleeper'].includes(collectionName)) {
                const appOdds = collection.getAppOdds();
                fieldsToAdd.push({ name: `${firstFieldName}`, value: `Odds: **${appOdds}**\nO: ${doc.model_over_percent}\nTie: ${tiePercent}%\nU: ${doc.model_under_percent}`, inline: false });
            }
        } else {
            if (['chalkboard', 'parlayplay', 'hotstreak', 'sleeper'].includes(collectionName)) {
                const appOdds = collection.getAppOdds();
                let fieldContent = `Odds: **${appOdds}**\nWin: ${datawisePercent}\n`;

                // Add appropriate EV display based on available data
                if (sharpEV === null) {
                    fieldContent += `Avg EV: ${avgEV}`;
                } else if (avgEV === sharpEV) {
                    fieldContent += `Sharp EV: ${sharpEV}`;
                } else {
                    fieldContent += `Sharp EV: ${sharpEV}\nAvg EV: ${avgEV}`;
                }

                fieldsToAdd.push({ name: `${firstFieldName}`, value: fieldContent, inline: false });
            }
        }

        // Handle traditional sportsbooks (Fliff, Hard Rock, Bovada, Caesars, etc.)
        if (tiePercent > 0) {
            // For bets with tie possibilities
            if (['fliff', 'hardrock', 'caesars', 'bovada', 'espnbet', 'betmgm', 'betrivers', 'betonline', 'betsaracen', 'oaklawn', 'fanatics', 'fanduel', 'rebet', 'novig', 'propbuilder'].includes(collectionName)) {
                const appOdds = collection.getAppOdds();
                fieldsToAdd.push({ name: `${firstFieldName}`, value: `Odds: **${appOdds}**\nO: ${doc.model_over_percent}\nTie: ${tiePercent}%\nU: ${doc.model_under_percent}`, inline: false });
            }
        } else {
            // For standard sportsbook bets
            if (sportsbookCollections.includes(collectionName)) {
                const appOdds = collection.getAppOdds();
                let fieldContent = `Odds: **${appOdds}**\nWin: ${datawisePercent}\n`;

                // Add EV information with special handling for Quick Kelly (QK) display
                if (sharpEV === null) {
                    fieldContent += `Avg EV: ${avgEV}`;
                } else {
                    fieldContent += `Sharp EV: ${sharpEV}`;

                    if (avgEV !== sharpEV) {
                        fieldContent += `\nAvg EV: ${avgEV}`;
                    }

                    // Add Quick Kelly calculation if available
                    if (doc.qk_display) {
                        fieldContent += `\nUnits: ${doc.qk_display}`;
                    }
                }
                fieldsToAdd.push({ name: `${firstFieldName}`, value: fieldContent, inline: false });
            }
        }

        // Add sharp odds comparison section for valid collections
        if (isCollectionNameValid(collectionName)) {
            // Group bookmakers by their lines for comparison
            let bookmakersByLine = {};

            // Helper function to format odds display based on the DataWise pick
            const createFieldValue = (oddsOver, oddsUnder, datawisePick) => {
                return datawisePick === "Over" ? `**${oddsOver}**/${oddsUnder}` : `**${oddsUnder}**/${oddsOver}`;
            };

            // Map for displaying alternative names for some sharp sources
            const sharpSourceDisplayName = {
                BetRivers: "Kambi",
            };

            // Group all sharp odds by their lines
            if (doc.sharps && Array.isArray(doc.sharps)) {
                doc.sharps.forEach(({ source, line, over_odds_american: oddsOver, under_odds_american: oddsUnder }) => {
                    if (line) {
                        const fieldValue = createFieldValue(oddsOver, oddsUnder, doc.datawise_pick);
                        if (!bookmakersByLine[line]) {
                            bookmakersByLine[line] = [];
                        }
                        let sourceName = sharpSourceDisplayName[source] || source;
                        bookmakersByLine[line].push(`${sourceName}: ${fieldValue}`);
                    }
                });
            } else {
                console.warn(`Document ${doc.proj_id || 'N/A'} is missing or has invalid 'sharps' array.`);
            }


            // Get the target line from the document
            const targetLine = doc.line;

            // Sort lines to prioritize the one matching the document's line
            const sortedLines = Object.keys(bookmakersByLine).sort((a, b) => {
                const lineA = parseFloat(a);
                const lineB = parseFloat(b);

                if (lineA === targetLine) return -1;
                if (lineB === targetLine) return 1;
                return lineA - lineB;
            });

            // Add sorted sharp odds comparison fields to the embed
            sortedLines.forEach(line => {
                const statType = doc.stat_type;
                const fieldName = `Sharp Odds: ${doc.datawise_pick} ${line} ${statType}`;
                const fieldValue = bookmakersByLine[line].join('\n');
                fieldsToAdd.push({
                    name: fieldName,
                    value: fieldValue,
                    inline: false
                });
            });
        }

        // Add matchup information for all valid collections
        if (doc.line) {
            if (isCollectionNameValid(collectionName)) {
                fieldsToAdd.push({ name: `Matchup`, value: `${matchup}`, inline: false });
            }

            // Add warning if Kambi is the only sharp source
            if (doc.sharps && Array.isArray(doc.sharps)) {
                const sharpSources = doc.sharps.map(sharp => sharp.source);
                 if (sharpSources.length === 1 && sharpSources[0] === 'BetRivers') {
                    fieldsToAdd.push({ name: 'Note', value: '🚨 Kambi is the only comparison book', inline: false });
                 }
            }

        }

        // Add all accumulated fields to the embed
        if (fieldsToAdd.length > 0) {
            embed.addFields(fieldsToAdd);
        } else {
            console.log(`No fields to add for document ID: ${doc.proj_id} in collection: ${collectionName}`);
        }

        // Add HardRock bet link if the collection is HardRock
        if (collectionName.toLowerCase() === 'hardrock') {
            let deepLink = null;

            // Determine which link to use based on datawise_pick
            if (doc.datawise_pick === 'Over' && doc.over_deep_link) {
                deepLink = doc.over_deep_link;
            } else if (doc.datawise_pick === 'Under' && doc.under_deep_link) {
                deepLink = doc.under_deep_link;
            }

            // Add the link field if we have a valid link
            if (deepLink) {
                embed.addFields({ name: '\u200B', value: `[Place Bet](${deepLink})` });
            }
        }

        // Add to standard embeds if win percentage meets threshold
        if (datawisePercentNumeric >= 36) {
            let embedItem = { embeds: [embed], collectionName };
            // For PrizePicks, add a clickable link using proj_string in the message content
            if (collectionName === 'prizepicks') {
                const link = `https://app.prizepicks.com/?projections=${doc.proj_string}`;
                embedItem.content = `[Click Here - PrizePicks Link](${link})`; // Discord Markdown for clickable link
            }
            embeds.push(embedItem);
        }

        // Only process special plays if we have at least 2 sharps
        if (doc.sharps && doc.sharps.length >= 2) {
            // Process high EV plays (excludes pick6 and epick)
            const highEvResults = processHighEvPlays(doc, embed, metrics, collectionName);
            highEvEmbedsDFS.push(...highEvResults.dfs);
            highEvEmbedsSportsbooks.push(...highEvResults.sportsbook);

            // Process hammer plays (excludes pick6 and epick)
            const hammerResults = processHammerPlays(doc, embed, metrics, collectionName);
            hammerEmbedsDFS.push(...hammerResults.dfs);
            hammerEmbedsSportsbooks.push(...hammerResults.sportsbook);

            // Process longshot plays
            longshotEmbeds.push(...processLongshotPlays(doc, embed, metrics, collectionName));
        }
    }

    // Send all embeds in the appropriate order
    if (isDfsCollection) {
        // Send DFS high EV and hammer plays
        if (highEvEmbedsDFS.length > 0) {
            await sendEmbedsSequentially(highEvHookDFS, highEvEmbedsDFS, false, true);
        }
        if (hammerEmbedsDFS.length > 0) {
            await sendEmbedsSequentially(hammerHookDFS, hammerEmbedsDFS, true, false);
        }
    } else if (isSportsbookCollection) {
        // Send sportsbook high EV and hammer plays
        if (highEvEmbedsSportsbooks.length > 0) {
            await sendEmbedsSequentially(highEvHookSportsbooks, highEvEmbedsSportsbooks, false, true);
        }
        if (hammerEmbedsSportsbooks.length > 0) {
            await sendEmbedsSequentially(hammerHookSportsbooks, hammerEmbedsSportsbooks, true, false);
        }
    }

    // Send standard plays to collection-specific webhook
    await sendEmbedsSequentially(collectionHook, embeds);

    // Send longshot plays
    await sendEmbedsSequentially(longshotHook, longshotEmbeds);
}

module.exports = { sendEmbeds };