const { WebhookClient } = require('discord.js');
const { sleep } = require('../config/utils');
const { roleMap, dfsHighEvRoleMap, sportsbookHighEvRoleMap } = require('../config/discordConfig');

/**
 * Creates a Discord webhook client safely with error handling
 * @param {string} url - Discord webhook URL
 * @param {string} collectionName - Name of the collection for error reporting
 * @returns {WebhookClient|null} - Returns webhook client or null if creation fails
 */
function safeWebhookClient(url, collectionName) {
    if (!url) {
        console.warn(`Webhook URL for ${collectionName} is undefined or invalid.`);
        return null;
    }
    try {
        return new WebhookClient({ url });
    } catch (error) {
        console.error(`Error creating WebhookClient for ${collectionName}:`, error);
        return null;
    }
}

/**
 * Sends embeds sequentially while respecting Discord rate limits
 * @param {WebhookClient} hook - Discord webhook client to send messages
 * @param {Array} embedsList - List of embed items to send, each containing embeds and optional content
 * @param {boolean} isHammerHook - Whether this is for hammer plays
 * @param {boolean} isHighEvHook - Whether this is for high EV plays
 */
async function sendEmbedsSequentially(hook, embedsList, isHammerHook = false, isHighEvHook = false) {
    for (const embedItem of embedsList) {
        const { embeds, content, collectionName } = embedItem;
        if (!embeds || embeds.length === 0) {
            console.error('Attempted to send an empty embed, skipping...');
            continue;
        }

        try {
            // Initialize send options with embeds
            let sendOptions = { embeds };
            
            // Add content if present (e.g., PrizePicks link)
            if (content) {
                sendOptions.content = content;
            }

            // Add appropriate role mentions based on the type of play
            if (isHammerHook && roleMap[collectionName]) {
                sendOptions.content = (sendOptions.content || '') + ` <@&${roleMap[collectionName]}>`;
            } else if (isHighEvHook) {
                let roleId;
                if (dfsHighEvRoleMap[collectionName]) {
                    roleId = dfsHighEvRoleMap[collectionName];
                } else if (sportsbookHighEvRoleMap[collectionName]) {
                    roleId = sportsbookHighEvRoleMap[collectionName];
                }
                if (roleId) {
                    sendOptions.content = (sendOptions.content || '') + ` <@&${roleId}>`;
                }
            }
            console.log(`Sending to webhook: ${JSON.stringify(sendOptions)}`);

            // Send the message
            const response = await hook.send(sendOptions);

            // Handle Discord rate limiting
            if (response && response.headers) {
                const remaining = response.headers.get('x-ratelimit-remaining');
                const resetAfter = response.headers.get('x-ratelimit-reset-after');

                if (remaining === '0') {
                    const delay = resetAfter ? parseFloat(resetAfter) * 1000 : 1000;
                    await sleep(delay);
                }
            }
        } catch (error) {
            if (error.code === 429) {
                // Handle rate limit exceeded error
                const retryAfter = error.retryAfter ? error.retryAfter * 1000 : 1000;
                await sleep(retryAfter);
                continue; // Retry the same message
            } else {
                console.error("Failed to send embed:", error);
            }
        }
    }
}

module.exports = {
    safeWebhookClient,
    sendEmbedsSequentially
};