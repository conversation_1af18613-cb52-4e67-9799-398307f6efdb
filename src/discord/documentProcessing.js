/**
 * Helper functions for processing betting documents
 */

/**
 * Extracts key metrics from a collection
 * @param {Object} collection - The collection object containing betting data
 * @returns {Object} - Object containing extracted metrics
 */
function getDocMetrics(collection) {
    return {
        modelEv: collection.setModelEv(),
        tiePercent: collection.getTiePercent(),
        datawisePercent: collection.getDatawisePercent(),
        datawisePercentNumeric: collection.getDatawisePercentNumeric(),
        avgEVRaw: collection.getAvgEVRaw()
    };
}

/**
 * Determines if a document should be skipped based on its metrics and collection conditions
 * @param {Object} metrics - Document metrics
 * @param {Object} doc - The document being processed
 * @param {string} collectionName - Name of the collection
 * @returns {boolean} - Whether the document should be skipped
 */
function shouldSkipDoc(metrics, doc, collectionName) {
    if (metrics.modelEv < 0.01) return true;  // Skip if model EV is less than 1%
    if (metrics.tiePercent >= 5) return true;   // Skip if tie probability is 5% or higher
    if (metrics.avgEVRaw < 0.03 && doc.sharps.length < 2) {
        console.log(`Skipping document ${doc.proj_id} due to low average EV (${metrics.avgEVRaw}) and insufficient sharps (${doc.sharps.length}).`);
        return true;
    }
    if (collectionName === 'vivid' && metrics.tiePercent > 0) return true;  // Skip Vivid picks with any tie
    if (collectionName === 'hardrock' && (doc.stat_type === 'Home Runs' || doc.stat_type === 'Stolen Bases') && doc.datawise_pick === 'Under') return true;  // Skip specific Hard Rock bets

    // New condition: For lines of 8 or less, require at least one sharp with the exact same line
    // Don't apply this rule for betr collection
    if (doc.line && doc.line <= 8 && collectionName !== 'betr') {
        // Check if any sharp has the exact same line as the provider
        const hasMatchingSharpLine = doc.sharps.some(sharp => sharp.line === doc.line);
        if (!hasMatchingSharpLine) {
            console.log(`Skipping document ${doc.proj_id} with line ${doc.line} because no sharp has the exact same line.`);
            return true;
        }
    }

    // Note: 'pick6' and 'epick' don't have specific skip rules defined here, but are DFS apps.
    // Add specific rules for 'epick' here if needed in the future.

    return false;
}

module.exports = {
    getDocMetrics,
    shouldSkipDoc
};