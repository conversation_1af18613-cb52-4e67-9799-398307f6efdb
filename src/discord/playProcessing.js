const { dfsCollections, sportsbookCollections } = require('../config/collections');

/**
 * Validates sharp book conditions for hammer plays
 * @param {Array<string>} sharpSources - Array of sharp book sources
 * @returns {boolean} - Whether sharp book conditions are valid
 */
function validateSharpBooks(sharpSources) {
    // Count how many sharp books we have
    const sharpBookCount = sharpSources.length;

    // We need at least 2 sharp books to consider the play valid
    // This is more resilient to fluctuating sharp book availability
    return sharpBookCount >= 2;

    // Old implementation below - more restrictive and sensitive to changes:
    /*
    const hasBetOnlineLine = sharpSources.includes('BetOnline');
    const hasDraftKingsLine = sharpSources.includes('DraftKings');
    const hasCaesarsLine = sharpSources.includes('Caesars');
    const hasFanduelLine = sharpSources.includes('Fanduel');
    const hasBetRiversLine = sharpSources.includes('BetRivers');
    const hasCircaLine = sharpSources.includes('Circa');
    const hasESPNBetLine = sharpSources.includes('ESPNBet');

    // Check that we don't have conflicting sharp book signals
    return !(
        (hasBetOnlineLine && !hasDraftKingsLine && !hasFanduelLine && !hasBetRiversLine && !hasCircaLine && !hasESPNBetLine) ||
        (hasDraftKingsLine && !hasBetOnlineLine && !hasFanduelLine && !hasBetRiversLine && !hasCircaLine && !hasESPNBetLine) ||
        (hasCaesarsLine && !hasDraftKingsLine && !hasFanduelLine && !hasBetRiversLine && !hasCircaLine && !hasESPNBetLine) ||
        (hasFanduelLine && !hasDraftKingsLine && !hasCaesarsLine && !hasBetRiversLine && !hasCircaLine && !hasESPNBetLine) ||
        (hasBetRiversLine && !hasDraftKingsLine && !hasCaesarsLine && !hasFanduelLine && !hasCircaLine && !hasESPNBetLine) ||
        (hasCircaLine && !hasDraftKingsLine && !hasCaesarsLine && !hasFanduelLine && !hasBetRiversLine && !hasESPNBetLine)
    );
    */
}

/**
 * Creates a PrizePicks link for the document if applicable
 * @param {Object} doc - The document being processed
 * @param {string} collectionName - Name of the collection
 * @returns {string|null} - The PrizePicks link content or null
 */
function createPrizePicksLink(doc, collectionName) {
    if (collectionName === 'prizepicks' && doc.proj_string) {
        const link = `https://app.prizepicks.com/?projections=${doc.proj_string}`;
        return `[Click Here - PrizePicks Link](${link})`;
    }
    return null;
}

/**
 * Processes high EV plays
 * @param {Object} doc - The document being processed
 * @param {Object} embed - The Discord embed
 * @param {Object} metrics - Document metrics
 * @param {string} collectionName - Name of the collection
 * @returns {Object} - Arrays of high EV embeds for DFS and Sportsbooks
 */
function processHighEvPlays(doc, embed, metrics, collectionName) {
    const { avgEVRaw } = metrics;
    const isDfsCollection = dfsCollections.includes(collectionName);
    const isSportsbookCollection = sportsbookCollections.includes(collectionName);

    // Create PrizePicks link if applicable
    const content = createPrizePicksLink(doc, collectionName);

    // Exclude pick6 and epick from High EV channel
    if (avgEVRaw >= 0.05 && collectionName !== 'pick6' && collectionName !== 'epick') {
        if (isDfsCollection) {
            return { dfs: [{ embeds: [embed], collectionName, content }], sportsbook: [] };
        } else if (isSportsbookCollection) {
            return { dfs: [], sportsbook: [{ embeds: [embed], collectionName, content }] };
        }
    }
    return { dfs: [], sportsbook: [] };
}

/**
 * Processes hammer plays
 * @param {Object} doc - The document being processed
 * @param {Object} embed - The Discord embed
 * @param {Object} metrics - Document metrics
 * @param {string} collectionName - Name of the collection
 * @returns {Object} - Arrays of hammer embeds for DFS and Sportsbooks
 */
function processHammerPlays(doc, embed, metrics, collectionName) {
    const { avgEVRaw, modelEv, datawisePercentNumeric } = metrics;
    const evToUse = avgEVRaw !== null ? avgEVRaw : modelEv;
    const isDfsCollection = dfsCollections.includes(collectionName);
    const isSportsbookCollection = sportsbookCollections.includes(collectionName);

    // Create PrizePicks link if applicable
    const content = createPrizePicksLink(doc, collectionName);

    // Soccer Specific Rule: Check unique sharps before proceeding
    if (doc.league?.toLowerCase() === 'soccer') {
        if (!doc.sharps || doc.sharps.length < 2) {
             // Not enough sharps overall for soccer hammer consideration
            return { dfs: [], sportsbook: [] };
        }
        const uniqueSharpSources = new Set(doc.sharps.map(sharp => sharp.source));
        if (uniqueSharpSources.size < 2) {
            console.log(`Skipping Hammer Play (Soccer): Proj ID ${doc.proj_id || 'N/A'} - Less than 2 unique sharp sources (${uniqueSharpSources.size}).`);
            // Fewer than 2 unique sharps, doesn't qualify as a soccer hammer play
            return { dfs: [], sportsbook: [] };
        }
    }

    // Exclude pick6 and epick from Hammer channel
    if ((evToUse >= 0.05 && datawisePercentNumeric >= 60 || evToUse >= 0.1 && datawisePercentNumeric >= 55) && collectionName !== 'pick6' && collectionName !== 'epick') {
        const sharpSources = doc.sharps.map(sharp => sharp.source);

        if ((isDfsCollection && validateSharpBooks(sharpSources)) || 
            (isSportsbookCollection && validateSharpBooks(sharpSources))) {
            if (isDfsCollection) {
                return { dfs: [{ embeds: [embed], collectionName, content }], sportsbook: [] };
            } else if (isSportsbookCollection) {
                return { dfs: [], sportsbook: [{ embeds: [embed], collectionName, content }] };
            }
        }
    }
    return { dfs: [], sportsbook: [] };
}

/**
 * Processes longshot plays
 * @param {Object} doc - The document being processed
 * @param {Object} embed - The Discord embed
 * @param {Object} metrics - Document metrics
 * @param {string} collectionName - Name of the collection
 * @returns {Array} - Array of longshot embeds
 */
function processLongshotPlays(doc, embed, metrics, collectionName) {
    const { datawisePercentNumeric } = metrics;
    const allLinesMatch = doc.sharps.every(sharp => sharp.line === doc.line);

    // Create PrizePicks link if applicable
    const content = createPrizePicksLink(doc, collectionName);

    if (datawisePercentNumeric <= 35 && allLinesMatch) {
        return [{ embeds: [embed], collectionName, content }];
    }
    return [];
}

module.exports = {
    validateSharpBooks,
    processHighEvPlays,
    processHammerPlays,
    processLongshotPlays
};