# Adding a New Collection to EV Monitor V2

This guide outlines the steps required to integrate a new betting platform (collection) into the EV Monitor V2 system.

## 1. Configuration Files

You need to update several configuration files to register the new collection.

### a. `src/config/collections.js`

-   Add the collection's lowercase name (e.g., `'newplatform'`) to the appropriate array:
    -   `dfsCollections`: For Daily Fantasy Sports platforms.
    -   `sportsbookCollections`: For traditional sportsbooks.

**Example:**

```js
// If 'newplatform' is a DFS platform
const dfsCollections = [..., 'newplatform'];

// If 'newplatform' is a Sportsbook
const sportsbookCollections = [..., 'newplatform'];

b. src/config/dbConfig.js

Add an entry in the collections object. The key is the collection name, and the value is the corresponding environment variable for its MongoDB collection name.

Example:

module.exports = {
  // ... other config
  collections: {
    // ... existing collections
    newplatform: process.env.COLLECTION_NEWPLATFORM, // Add this line
    epick: process.env.COLLECTION_EPICK // Example for Epick
  }
};
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Js
IGNORE_WHEN_COPYING_END
c. src/config/discordConfig.js

Webhook URL: Add an entry in webhookUrls. The key is the collection name, and the value is the environment variable for its specific Discord webhook URL.

Role ID: Add an entry in roleMap. The key is the collection name, and the value is the environment variable for the Discord role ID to ping for this collection's standard plays (used for Hammer plays by default).

(Optional) High EV Role ID: If the platform should have separate High EV alerts and pings, add an entry to dfsHighEvRoleMap or sportsbookHighEvRoleMap. The key is the collection name, and the value is the environment variable for its High EV role ID. Note: Currently, 'pick6' and 'epick' do not have High EV roles configured.

Example:

module.exports = {
  webhookUrls: {
    // ... existing webhooks
    newplatform: process.env.NEWPLATFORM, // Add webhook URL mapping
    epick: process.env.EPICK // Example for Epick
  },
  // ... other config
  roleMap: {
    // ... existing roles
    newplatform: process.env.NEWPLATFORM_ROLE_ID, // Add role ID mapping
    epick: process.env.EPICK_ROLE_ID // Example for Epick
  },
  dfsHighEvRoleMap: {
      // ... existing DFS high EV roles
      // Add 'newplatform: process.env.NEWPLATFORM_HIGH_EV_ROLE_ID' ONLY if needed
  },
  sportsbookHighEvRoleMap: {
      // ... existing SB high EV roles
      // Add 'newplatform: process.env.NEWPLATFORM_HIGH_EV_ROLE_ID' ONLY if needed
  }
  // ... other config
};
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Js
IGNORE_WHEN_COPYING_END
2. Database Collection Class
a. src/database/collections.js

Define a new class for the collection that extends the base Collection class.

Implement the getAuthor() method, returning an object with name and iconURL (use a direct URL or an environment variable like process.env.NEWPLATFORM_IMG).

(Optional) Override other methods (getAppOdds, getMulti, getFirstFieldName, getDescription, getColor, etc.) if the new platform requires custom logic or display formats different from the base class. Refer to existing classes like Underdog, Sleeper, BetMGM, ProphetX for examples.

Add the new class to the collectionMap at the bottom of the file, mapping the collection name string to the class definition.

Example:

// Define the class
class NewPlatform extends Collection {
    getAuthor() {
        return {
            name: 'New Platform Name', // Replace with actual name
            iconURL: 'https://link-to-your/logo.png' // Replace with actual URL or process.env variable
        };
    }
    // Add other method overrides here if needed
}

// Add Epick Class Example
class Epick extends Collection {
    getAuthor() {
        return {
            name: 'Epick',
            iconURL: 'https://github.com/datawisebets/logos/blob/f5ed14fc4dda9c958e25611848e197806a5de786/epick.png?raw=true'
        };
    }
}


// Add to the map
const collectionMap = {
    // ... existing map entries
    'newplatform': NewPlatform, // Add this line
    'epick': Epick // Example for Epick
};

module.exports = { collectionMap };
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Js
IGNORE_WHEN_COPYING_END
3. Embed Generation & Filtering Logic

Review and potentially update the following files if the new collection has unique requirements for how data is displayed or filtered:

a. src/discord/documentProcessing.js

shouldSkipDoc: Add specific rules here if certain plays from this platform should be skipped based on criteria like tiePercent, stat_type, line, etc.

b. src/discord/playProcessing.js

processHighEvPlays, processHammerPlays: Check if this collection should be excluded from these special channels (like 'pick6' and 'epick' currently are). Add && collectionName !== 'newplatform' to the conditions if necessary.

createPrizePicksLink: Add logic here if the platform has a specific URL structure for deep-linking bets (similar to PrizePicks).

c. src/discord/discordSender.js

Review the sections that add fields to the embed based on collectionName. Add || collectionName === 'newplatform' or ['list', 'of', 'apps', 'newplatform'].includes(collectionName) to the appropriate if conditions if the new platform follows the display logic of existing platforms (e.g., fixed odds display, custom multiplier display, sportsbook odds display).

Add specific logic for the new platform if its embed structure is unique.

4. Main Processing Loop
a. src/main.js

Add a call to processCollection('newplatform') within the Promise.allSettled block in the main function.

Example:

async function main() {
  // ...
    await Promise.allSettled([
      // ... existing processCollection calls
      processCollection('newplatform'), // Add this line
      processCollection('epick'), // Example for Epick
    ]);
  // ...
}
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Js
IGNORE_WHEN_COPYING_END
5. Environment Variables

Ensure the following environment variables are defined in your .env file or system environment:

COLLECTION_NEWPLATFORM: The exact name of the MongoDB collection for this platform.

NEWPLATFORM: The Discord webhook URL for the platform's dedicated channel.

NEWPLATFORM_ROLE_ID: The Discord role ID for general pings for this platform.

NEWPLATFORM_HIGH_EV_ROLE_ID (Optional): Role ID for High EV pings, if applicable.

NEWPLATFORM_IMG (Optional): If used in the getAuthor method in src/database/collections.js.

Example .env entries:

COLLECTION_NEWPLATFORM=new_platform_data
NEWPLATFORM=https://discord.com/api/webhooks/.../...
NEWPLATFORM_ROLE_ID=123456789012345678

# For Epick Example
COLLECTION_EPICK=epick_data
EPICK=https://discord.com/api/webhooks/1366461947356385350/qDyk8rWsNTTIDYUHktpal44gPdIsnLetj1fIHCaNkPrQf2s6vOb1i839mJC1KMLn52AJ
EPICK_ROLE_ID=1366462934963720314
# EPICK_HIGH_EV_ROLE_ID=... (Only if needed)
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Bash
IGNORE_WHEN_COPYING_END
6. Restart Application
After making all the code changes and updating environment variables, restart the Node.js application (npm start or similar command) for the changes to take effect.